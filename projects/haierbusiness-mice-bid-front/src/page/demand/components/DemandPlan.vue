<script setup lang="ts">
// 日程安排
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, computed, defineProps, defineEmits, defineExpose, nextTick } from 'vue';
import {
  DemandTypeConstant,
  DemandSubmitObj,
  StaysArr,
  PlacesArr,
  CateringsArr,
  VehiclesArr,
  AttendantsArr,
  ActivitiesArr,
  InsurancesArr,
} from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

import StayCom from './dailySchedule/StayCom.vue';
import PlaceCom from './dailySchedule/PlaceCom.vue';
import CateringCom from './dailySchedule/CateringCom.vue';
import VehicleCom from './dailySchedule/VehicleCom.vue';
import AttendantCom from './dailySchedule/AttendantCom.vue';
import ActivityCom from './dailySchedule/ActivityCom.vue';
import InsuranceCom from './dailySchedule/InsuranceCom.vue';

import dayjs, { Dayjs } from 'dayjs';

const props = defineProps({
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  hotelList: {
    type: Array,
    default: [],
  },
  cacheStr: {
    type: String,
    default: '',
  },
  meetingMinDaysNum: {
    type: Number,
    default: 0,
  },
  demandSets: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanFunc', 'demandPlanDateChangeFunc']);

const loadingEnd = ref(false); // 是否加载完毕

const demandStayRef = ref([]);
const demandPlaceRef = ref([]);
const demandCateringRef = ref([]);
const demandVehicleRef = ref([]);
const demandAttendantRef = ref([]);
const demandActivityRef = ref([]);
const demandInsuranceRef = ref([]);

// 保险产品列表相关状态
const insuranceProductList = ref<Array<any>>([]);
const insuranceSearchName = ref(''); // 保险名称搜索
const insurancePageNum = ref(1); // 当前页码
const insurancePageSize = ref(10); // 每页条数
const insuranceTotal = ref(0); // 总条数
const insuranceLoading = ref(false); // 加载状态
const insuranceHasMore = ref(true); // 是否还有更多数据

// 折叠面板-默认展开
const activeKey = ref<Array<number>>([0]);

const demandTypeList = ref<Array<any>>([]); // 需求列表

const dateRange = ref<Array<string>>([]); // 日期
const dateRangeList = ref<Array<string>>([]); // 所有日期列表
const planList = ref<Array<any>>([]); // 日程安排
const rangeDisabled = ref<boolean>(false); // 日程安排不可选

// 日程安排表单
const formState = reactive<DemandSubmitObj>({
  startDate: '', // 需求开始时间
  endDate: '', // 需求结束时间
});

const planShow = ref<boolean>(false); // 复制需求-弹窗
const copyData = ref<String>(''); // 复制需求
const beCopiedIndex = ref(null); // 被复制需求index
const copyIndex = ref(null); // 复制需求index

const coverShow = ref<boolean>(false); // 复制覆盖-弹窗
const timerChange = ref<boolean>(false); // 时间是否变化

// 日程安排
const planParams = ref<DemandSubmitObj>({
  stays: [], // 住宿
  places: [], // 会场
  caterings: [], // 用餐
  vehicles: [], // 用车
  attendants: [], // 服务人员
  activities: [], // 拓展活动
  insurances: [], // 保险
});

// 住宿
const staysParams = ref<StaysArr>({
  tempDemandHotelId: null, // 需求酒店id
  level: null, // 酒店星级
  demandDate: '', // 需求日期
  roomType: null, // 房型
  breakfastType: 1, // 早餐类型
  personNum: null, // 人数
  roomNum: null, // 入住房间数
  discrepancyReason: '', // 不一致原因
  calcUnitPrice: null, // 自动测算单价
});
// 会场
const placesParams = ref<PlacesArr>({
  tempDemandHotelId: null, // 需求酒店id
  level: null,
  demandDate: '', // 需求日期
  usageTime: null, // 使用时间
  usagePurpose: null, // 使用用途
  personNum: null, // 人数
  area: null, // 面积
  underLightFloor: null, // 灯下层高
  tableType: null, // 摆台形式
  placeNum: 1, // 会厅数量

  hasLed: false, // 是否需要led
  ledNum: null, // led数量
  ledSpecs: null, // led规格说明

  hasTea: null, // 是否需要茶歇
  teaEachTotalPrice: null, // 茶歇标准/每人
  teaDesc: null, // 茶歇说明

  calcUnitPlacePrice: null, // 自动测算会场单价
  calcUnitLedPrice: null, // 自动测算led单价
  calcUnitTeaPrice: null, // 自动测算茶歇单价
});
// 用餐
const cateringsParams = ref<CateringsArr>({
  tempDemandHotelId: null, // 需求酒店id
  isInsideHotel: null, // 是否酒店提供用餐
  demandDate: '', // 需求日期
  cateringType: null, // 餐饮类型
  cateringTime: null, // 用餐时间
  personNum: null, // 人数
  demandUnitPrice: null, // 用餐标准
  isIncludeDrinks: false, // 是否包含酒水

  calcUnitPrice: null, // 自动测算单价
});
// 用车
const vehiclesParams = ref<VehiclesArr>({
  demandDate: '', // 需求日期
  usageType: null, // 使用方式
  usageTime: null, // 使用时长
  seats: null, // 座位数
  vehicleNum: null, // 车辆数量
  brand: null, // 品牌
  route: null, // 路线,多程逗号分隔
  routeList: [], // 路线
  remarks: null, // 路线概述

  calcUnitPrice: null, // 自动测算单价
});
// 服务人员
const attendantsParams = ref<AttendantsArr>({
  demandDate: '', // 需求日期
  type: null, // 人员类型
  personNum: null, // 人数
  duty: null, // 工作范围

  calcUnitPrice: null, // 自动测算单价
});
// 拓展活动
const activitiesParams = ref<ActivitiesArr>({
  demandDate: '', // 需求日期
  demandUnitPrice: null, // 费用标准
  personNum: null, // 人数
  desc: null, // 活动说明
  fileList: [], // 上传文件
  paths: [],

  calcUnitPrice: null, // 自动测算单价
});
// 保险
const insurancesParams = ref<InsurancesArr>({
  demandDate: '', // 需求日期
  demandUnitPrice: null, // 需求单价
  personNum: null, // 参保人数
  productId: null, // 保险产品id
  productMerchantId: null, // 产品所属商户id
  insuranceName: null, // 险种名称
  insuranceContent: null, // 险种条目

  calcUnitPrice: null, // 自动测算单价
});

// 日程是否为空
const isPlanEmpty = computed(() => {
  const isEmpty = planList.value.map((e) => {
    return (
      e.stays.length === 0 &&
      e.places.length === 0 &&
      e.caterings.length === 0 &&
      e.vehicles.length === 0 &&
      e.attendants.length === 0 &&
      e.activities.length === 0 &&
      e.insurances.length === 0
    );
  });

  return isEmpty;
});

//
// 时间选择模块
//
// 相差天数
const calculateDateDifference = (date1: String, date2: String) => {
  // 将日期转换为Date对象
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // 计算毫秒差值
  const diffTime = Math.abs(d2.getTime() - d1.getTime());

  // 将毫秒转换为天数
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};
// 日期加N天
const getNextDay = (date: String, num: Number) => {
  // 创建一个新的 Date 对象
  var nextDay = new Date(date);
  // 获取当前日期的天数并加n
  nextDay.setDate(nextDay.getDate() + num);
  // 返回加一天后的日期字符串
  return nextDay.toISOString().split('T')[0];
  // return nextDay.toISOString().split('T')[0].replace(/-/g, '/');
};

// 不可选日期
const disabledDate = (current: Dayjs) => {
  // 当前日期之前的日期都禁用
  return (
    current &&
    current <
      dayjs()
        .add(props.meetingMinDaysNum - 1, 'day')
        .endOf('day')
  );
};
// 时间选择
const changeDate = (date: Array) => {
  formState.startDate = date ? date[0] : '';
  formState.endDate = date ? date[1] : '';

  // 时间是否变化
  timerChange.value = true;
};
const openChangeDate = (status) => {
  // 关闭日历 && 时间发生变化 时，提示用户是否覆盖数据
  if (!status && timerChange.value) {
    coverShow.value = true;
    timerChange.value = true;
  }
};

// 需求覆盖
const handleCover = () => {
  dateRangeList.value = [];
  let coverList = [];

  if (formState.startDate && formState.endDate) {
    // 相差天数
    const returnInt = calculateDateDifference(formState.startDate, formState.endDate);

    for (let index = 0; index <= returnInt; index++) {
      // 日期+n
      dateRangeList.value.push(getNextDay(formState.startDate, index));
      const coverParams =
        planList.value.length > index ? planList.value[index] : JSON.parse(JSON.stringify(planParams.value));

      if (coverParams.stays) {
        coverParams.stays.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.places) {
        coverParams.places.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.caterings) {
        coverParams.caterings.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.vehicles) {
        coverParams.vehicles.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.attendants) {
        coverParams.attendants.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.activities) {
        coverParams.activities.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }
      if (coverParams.insurances) {
        coverParams.insurances.forEach((e) => {
          e.demandDate = getNextDay(formState.startDate, index);
        });
      }

      coverList.push({
        ...coverParams,
        demandDate: getNextDay(formState.startDate, index),
        key: Date.now(),
      });
    }

    // 需求覆盖
    planList.value = [...coverList];

    // 默认展开
    activeKey.value = planList.value.map((e, i) => {
      return i;
    });

    emit('demandPlanFunc', {
      ...formState,
      planList: [...planList.value],
    });

    emit('demandPlanDateChangeFunc', true);
  }

  coverShow.value = false;
};

// 需求不覆盖
const handleNotCover = () => {
  dateRangeList.value = [];
  planList.value = [];

  if (formState.startDate && formState.endDate) {
    // 相差天数
    const returnInt = calculateDateDifference(formState.startDate, formState.endDate);

    for (let index = 0; index <= returnInt; index++) {
      // 日期+n
      dateRangeList.value.push(getNextDay(formState.startDate, index));
      planList.value.push({
        ...JSON.parse(JSON.stringify(planParams.value)),
        demandDate: getNextDay(formState.startDate, index),
        key: Date.now(),
      });
    }

    // 默认展开
    activeKey.value = planList.value.map((e, i) => {
      return i;
    });

    emit('demandPlanFunc', {
      ...formState,
      planList: [...planList.value],
    });
  }

  coverShow.value = false;
};

//
// 日程安排
//
// 复制需求
const copyPlan = (dateIndex: Number) => {
  copyData.value = null;
  copyIndex.value = dateIndex;
  planShow.value = true;
};
const copyChange = (index: Number) => {
  beCopiedIndex.value = index;
};
const handleCopy = () => {
  if (!copyData.value) {
    message.error('请选择要复制的需求日期');
    return;
  }

  // 被复制日期
  const copyParams = JSON.parse(JSON.stringify(planList.value[beCopiedIndex.value]));

  // 日期赋值
  const newDemandDate = planList.value[copyIndex.value].demandDate;
  copyParams.demandDate = newDemandDate;

  copyParams.stays.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.places.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.caterings.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.vehicles.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.attendants.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.activities.forEach((e) => {
    e.demandDate = newDemandDate;
  });
  copyParams.insurances.forEach((e) => {
    e.demandDate = newDemandDate;
  });

  // 新日期赋值
  planList.value[copyIndex.value] = JSON.parse(JSON.stringify(copyParams));

  nextTick(() => {
    try {
      demandStayRef.value[copyIndex.value].changePlanList();
      demandPlaceRef.value[copyIndex.value].changePlanList();
      demandCateringRef.value[copyIndex.value].changePlanList();
      demandVehicleRef.value[copyIndex.value].changePlanList();
      demandAttendantRef.value[copyIndex.value].changePlanList();
      demandActivityRef.value[copyIndex.value].changePlanList();
      demandInsuranceRef.value[copyIndex.value].changePlanList();

      emit('demandPlanDateChangeFunc', true);
    } catch (error) {}
  });

  closeModal();
};
const closeModal = () => {
  planShow.value = false;
};

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 添加新需求
const planAdd = (date: string, type: string, dateIndex: number) => {
  const hotelsList = props.hotelList.filter((e) => e.centerMarker) || [];

  setTimeout(() => {
    switch (type) {
      case 'type_stays':
        // 住宿
        planList.value[dateIndex].stays.push({
          ...JSON.parse(JSON.stringify(staysParams.value)),
          tempDemandHotelId: hotelsList?.length === 1 ? hotelsList[0].tempDemandHotelId : null,
          level: hotelsList?.length === 1 ? hotelsList[0].level : null,
          demandDate: date,
        });

        demandStayRef.value[dateIndex].changePlanList();

        anchorId('stayId' + (demandStayRef.value.length - 1));
        break;

      case 'type_places':
        // 会场
        planList.value[dateIndex].places.push({
          ...JSON.parse(JSON.stringify(placesParams.value)),
          tempDemandHotelId: hotelsList?.length === 1 ? hotelsList[0].tempDemandHotelId : null,
          level: hotelsList?.length === 1 ? hotelsList[0].level : null,
          demandDate: date,
        });

        demandPlaceRef.value[dateIndex].changePlanList();

        anchorId('placeId' + (demandPlaceRef.value.length - 1));
        break;

      case 'type_caterings':
        // 用餐
        planList.value[dateIndex].caterings.push({
          ...JSON.parse(JSON.stringify(cateringsParams.value)),
          tempDemandHotelId: hotelsList?.length === 1 ? hotelsList[0].tempDemandHotelId : null,
          level: hotelsList?.length === 1 ? hotelsList[0].level : null,
          demandDate: date,
        });

        demandCateringRef.value[dateIndex].changePlanList();

        anchorId('cateringId' + (demandCateringRef.value.length - 1));
        break;

      case 'type_vehicles':
        // 用车
        planList.value[dateIndex].vehicles.push({
          ...JSON.parse(JSON.stringify(vehiclesParams.value)),
          demandDate: date,
        });

        demandVehicleRef.value[dateIndex].changePlanList();

        anchorId('vehicleId' + (demandVehicleRef.value.length - 1));
        break;

      case 'type_attendants':
        // 服务人员
        planList.value[dateIndex].attendants.push({
          ...JSON.parse(JSON.stringify(attendantsParams.value)),
          demandDate: date,
        });

        demandAttendantRef.value[dateIndex].changePlanList();

        anchorId('attendantId' + (demandAttendantRef.value.length - 1));
        break;

      case 'type_activities':
        // 拓展活动
        planList.value[dateIndex].activities.push({
          ...JSON.parse(JSON.stringify(activitiesParams.value)),
          demandDate: date,
        });

        demandActivityRef.value[dateIndex].changePlanList();

        anchorId('activityId' + (demandActivityRef.value.length - 1));
        break;

      case 'type_insurances':
        // 保险
        planList.value[dateIndex].insurances.push({
          ...JSON.parse(JSON.stringify(insurancesParams.value)),
          demandDate: date,
        });

        demandInsuranceRef.value[dateIndex].changePlanList();

        anchorId('insuranceId' + (demandInsuranceRef.value.length - 1));
        break;

      default:
        message.error('需求添加错误');
        break;
    }
  }, 300);
};

// 住宿
const demandPlanStayFunc = (obj: StaysArr) => {
  const { list, index } = obj;
  planList.value[index].stays = [...list];
};
// 会场
const demandPlanPlaceFunc = (obj: PlacesArr) => {
  const { list, index } = obj;
  planList.value[index].places = [...list];
};
// 用餐
const demandPlanCateringFunc = (obj: CateringsArr) => {
  const { list, index } = obj;
  planList.value[index].caterings = [...list];
};
// 用车
const demandPlanVehicleFunc = (obj: VehiclesArr) => {
  const { list, index } = obj;
  planList.value[index].vehicles = [...list];
};
// 服务人员
const demandPlanAttendantFunc = (obj: AttendantsArr) => {
  const { list, index } = obj;
  planList.value[index].attendants = [...list];
};
// 拓展活动
const demandPlanActivityFunc = (obj: ActivitiesArr) => {
  const { list, index } = obj;
  planList.value[index].activities = [...list];
};
// 保险
const demandPlanInsuranceFunc = (obj: InsurancesArr) => {
  const { list, index } = obj;
  planList.value[index].insurances = [...list];
};

// 删除
const demandPlanRemoveFunc = (removeObj: DemandSubmitObj) => {
  const { type, delIndex, index } = removeObj;

  switch (type) {
    case 'stay':
      // 住宿
      planList.value[index].stays.splice(delIndex, 1);
      demandStayRef.value[index].changePlanList();

      break;

    case 'place':
      // 会场
      planList.value[index].places.splice(delIndex, 1);
      demandPlaceRef.value[index].changePlanList();

      break;

    case 'catering':
      // 用餐
      planList.value[index].caterings.splice(delIndex, 1);
      demandCateringRef.value[index].changePlanList();

      break;

    case 'vehicle':
      // 住车
      planList.value[index].vehicles.splice(delIndex, 1);
      demandVehicleRef.value[index].changePlanList();

      break;

    case 'attendant':
      // 服务人员
      planList.value[index].attendants.splice(delIndex, 1);
      demandAttendantRef.value[index].changePlanList();

      break;

    case 'activity':
      // 拓展活动
      planList.value[index].activities.splice(delIndex, 1);
      demandActivityRef.value[index].changePlanList();

      break;

    case 'insurance':
      // 保险
      planList.value[index].insurances.splice(delIndex, 1);
      demandInsuranceRef.value[index].changePlanList();

      break;

    default:
      break;
  }
};

const onSubmitPlan = async () => {
  let isAllValid = true;

  // 住宿
  if (demandStayRef.value) {
    const stayValid = await Promise.all(demandStayRef.value.map((comp) => comp?.onSubmit?.()));
    if (stayValid.includes(false)) {
      message.error('请完善住宿信息');

      // 定位到第一个未通过的面板
      const index = stayValid.findIndex((item) => item === false);
      anchorId('stayId' + index);

      isAllValid = false;
    }
  }

  // 会场
  if (demandPlaceRef.value && isAllValid) {
    const placeValid = await Promise.all(demandPlaceRef.value.map((comp) => comp?.onSubmit?.()));
    if (placeValid.includes(false)) {
      message.error('请完善会场信息');

      // 定位到第一个未通过的面板
      const index = placeValid.findIndex((item) => item === false);
      anchorId('placeId' + index);

      isAllValid = false;
    }
  }

  // 用餐
  if (demandCateringRef.value && isAllValid) {
    const cateringValid = await Promise.all(demandCateringRef.value.map((comp) => comp?.onSubmit?.()));
    if (cateringValid.includes(false)) {
      message.error('请完善用餐信息');

      // 定位到第一个未通过的面板
      const index = cateringValid.findIndex((item) => item === false);
      anchorId('cateringId' + index);

      isAllValid = false;
    }
  }

  // 用车
  if (demandVehicleRef.value && isAllValid) {
    const vehicleValid = await Promise.all(demandVehicleRef.value.map((comp) => comp?.onSubmit?.()));
    if (vehicleValid.includes(false)) {
      message.error('请完善用车信息');

      // 定位到第一个未通过的面板
      const index = vehicleValid.findIndex((item) => item === false);
      anchorId('vehicleId' + index);

      isAllValid = false;
    }
  }

  // 服务人员
  if (demandAttendantRef.value && isAllValid) {
    const attendantValid = await Promise.all(demandAttendantRef.value.map((comp) => comp?.onSubmit?.()));
    if (attendantValid.includes(false)) {
      message.error('请完善服务人员信息');

      // 定位到第一个未通过的面板
      const index = attendantValid.findIndex((item) => item === false);
      anchorId('attendantId' + index);

      isAllValid = false;
    }
  }

  // 拓展活动
  if (demandActivityRef.value && isAllValid) {
    const activityValid = await Promise.all(demandActivityRef.value.map((comp) => comp?.onSubmit?.()));
    if (activityValid.includes(false)) {
      message.error('请完善拓展活动信息');

      // 定位到第一个未通过的面板
      const index = activityValid.findIndex((item) => item === false);
      anchorId('activityId' + index);

      isAllValid = false;
    }
  }

  // 保险
  if (demandInsuranceRef.value && isAllValid) {
    const insuranceValid = await Promise.all(demandInsuranceRef.value.map((comp) => comp?.onSubmit?.()));
    if (insuranceValid.includes(false)) {
      message.error('请完善保险信息');

      // 定位到第一个未通过的面板
      const index = insuranceValid.findIndex((item) => item === false);
      anchorId('insuranceId' + index);

      isAllValid = false;
    }
  }

  emit('demandPlanFunc', {
    ...formState,
    planList: [...planList.value],
  });

  return isAllValid;
};

// 暂存
const tempSave = () => {
  emit('demandPlanFunc', {
    ...formState,
    planList: [...planList.value],
  });
};

// 获取保险产品列表
const getInsuranceProductList = async (isSearch = false) => {
  if (insuranceLoading.value) return;

  if (isSearch) {
    insurancePageNum.value = 1;
    insuranceProductList.value = [];
    insuranceHasMore.value = true;
  }

  if (!insuranceHasMore.value) return;

  insuranceLoading.value = true;

  try {
    const params = {
      insuranceName: insuranceSearchName.value,
      pageNum: insurancePageNum.value,
      pageSize: insurancePageSize.value,
    };

    const res = await demandApi.insuranceProductList(params);
    console.log(res, '999999');

    if (res && res.records) {
      const newData = res.records || [];

      if (isSearch) {
        insuranceProductList.value = newData;
      } else {
        insuranceProductList.value = [...insuranceProductList.value, ...newData];
      }

      insuranceTotal.value = res.total || 0;

      // 判断是否还有更多数据
      if (insuranceProductList.value.length >= insuranceTotal.value || newData.length < insurancePageSize.value) {
        insuranceHasMore.value = false;
      }
    }
  } catch (error) {
    console.error('获取保险产品列表失败:', error);
  } finally {
    insuranceLoading.value = false;
  }
};

// 下拉框滚动到底部
const handleInsurancePopupScroll = (e) => {
  const { target } = e;
  if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 10) {
    if (!insuranceLoading.value && insuranceHasMore.value) {
      insurancePageNum.value++;
      getInsuranceProductList();
    }
  }
};

// 搜索防抖
let insuranceSearchTimer = null;
const handleInsuranceSearch = (value) => {
  insuranceSearchName.value = value;

  if (insuranceSearchTimer) {
    clearTimeout(insuranceSearchTimer);
  }

  insuranceSearchTimer = setTimeout(() => {
    getInsuranceProductList(true);
  }, 300);
};

defineExpose({ onSubmitPlan, tempSave });

onMounted(async () => {
  // 流程编排
  demandTypeList.value = [];
  DemandTypeConstant.toArray().forEach((e) => {
    if (props.demandSets.includes(e.code)) {
      demandTypeList.value.push(e);
    }
  });

  // 获取保险产品列表
  await getInsuranceProductList();

  loadingEnd.value = false;

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);

    if (cacheObj.startDate && cacheObj.endDate) {
      // 日期反显
      dateRange.value = [cacheObj.startDate, cacheObj.endDate];
      formState.startDate = cacheObj.startDate;
      formState.endDate = cacheObj.endDate;
      await handleNotCover([...dateRange.value]);

      // 日程安排反显
      planList.value = [];

      dateRangeList.value.forEach((e, i) => {
        let stays = (cacheObj.stays && cacheObj.stays.filter((e1) => e === e1.demandDate)) || [];
        let places = (cacheObj.places && cacheObj.places.filter((e1) => e === e1.demandDate)) || [];
        let caterings = (cacheObj.caterings && cacheObj.caterings.filter((e1) => e === e1.demandDate)) || [];
        let vehicles = (cacheObj.vehicles && cacheObj.vehicles.filter((e1) => e === e1.demandDate)) || [];
        let attendants = (cacheObj.attendants && cacheObj.attendants.filter((e1) => e === e1.demandDate)) || [];
        let activities = (cacheObj.activities && cacheObj.activities.filter((e1) => e === e1.demandDate)) || [];
        let insurances = (cacheObj.insurances && cacheObj.insurances.filter((e1) => e === e1.demandDate)) || [];

        // 用餐
        caterings.forEach((e) => {
          e.isInsideHotel = e.isInsideHotel ? 1 : 0;
        });

        // 用车
        vehicles.forEach((e) => {
          if (e.route) {
            // 路线反显
            e.routeList = e.route.split(',');
          }
        });

        // 拓展活动-上传资料-反显
        activities.forEach((j) => {
          if (j.paths && j.paths.length > 0) {
            j.fileList = [];
            j.paths.forEach((g) => {
              let gObj = {};
              let isJson = true;
              try {
                gObj = JSON.parse(g);
              } catch (error) {
                isJson = false;
              }

              if (!isJson) return;

              j.fileList.push({ name: gObj.name, filePath: gObj.url });
            });
          }
        });

        planList.value.push({
          stays: stays,
          places: places,
          caterings: caterings,
          vehicles: vehicles,
          attendants: attendants,
          activities: activities,
          insurances: insurances,
          demandDate: e,
          key: Date.now() + i,
        });
      });

      emit('demandPlanFunc', {
        ...formState,
        planList: [...planList.value],
      });
    }
  }

  loadingEnd.value = true;
});
</script>

<template>
  <!-- 日程安排 -->
  <div class="demand_plan demand_pad24">
    <div class="demand_title ml8">
      <div class="demand_border"></div>
      <span>日程安排</span>

      <div class="demand_plan_date">
        <a-range-picker
          :disabled="rangeDisabled"
          v-model:value="dateRange"
          @change="changeDate"
          @openChange="openChangeDate"
          :disabledDate="disabledDate"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
          style="width: 100%"
          :allow-clear="false"
        />
      </div>
    </div>

    <!-- 折叠面板 -->
    <div class="date_plan mt24" v-if="loadingEnd">
      <a-collapse class="plan_collapse" v-model:activeKey="activeKey" :bordered="false" collapsible="disabled">
        <template #expandIcon="{ isActive }">
          <!-- <div class="plan_collapse_arrow" :style="{ transform: 'rotate(' + (isActive ? 180 : 0) + 'deg)' }"></div> -->
        </template>

        <a-collapse-panel v-for="(dateItem, dateIndex) in planList" :key="dateIndex" class="mb20">
          <template #header>
            <div :id="dateItem.demandDate" class="plan_collapse_title">{{ dateItem.demandDate }}</div>
          </template>
          <template #extra>
            <div v-if="isPlanEmpty[dateIndex]" class="plan_collapse_extra">
              <div class="extra_copy" @click.stop="copyPlan(dateIndex)">复制需求</div>
            </div>
          </template>
          <div class="plan_collapse_content">
            <!-- 住宿 -->
            <stay-com
              :id="'stayId' + dateIndex"
              ref="demandStayRef"
              :date-index="dateIndex"
              :meetingPersonTotal="props.meetingPersonTotal"
              :hotel-list="props.hotelList"
              :stay-list="dateItem.stays"
              @demandPlanStayFunc="demandPlanStayFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 会场 -->
            <place-com
              :id="'placeId' + dateIndex"
              ref="demandPlaceRef"
              :date-index="dateIndex"
              :meetingPersonTotal="props.meetingPersonTotal"
              :hotel-list="props.hotelList"
              :place-list="dateItem.places"
              @demandPlanPlaceFunc="demandPlanPlaceFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 用餐 -->
            <catering-com
              :id="'cateringId' + dateIndex"
              ref="demandCateringRef"
              :date-index="dateIndex"
              :meetingPersonTotal="props.meetingPersonTotal"
              :hotel-list="props.hotelList"
              :catering-list="dateItem.caterings"
              @demandPlanCateringFunc="demandPlanCateringFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 用车 -->
            <vehicle-com
              :id="'vehicleId' + dateIndex"
              ref="demandVehicleRef"
              :date-index="dateIndex"
              :vehicle-list="dateItem.vehicles"
              :hotel-list="props.hotelList"
              @demandPlanVehicleFunc="demandPlanVehicleFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 服务人员 -->
            <attendant-com
              :id="'attendantId' + dateIndex"
              ref="demandAttendantRef"
              :date-index="dateIndex"
              :attendant-list="dateItem.attendants"
              :hotel-list="props.hotelList"
              @demandPlanAttendantFunc="demandPlanAttendantFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 扩展活动 -->
            <activity-com
              :id="'activityId' + dateIndex"
              ref="demandActivityRef"
              :date-index="dateIndex"
              :meetingPersonTotal="props.meetingPersonTotal"
              :activity-list="dateItem.activities"
              @demandPlanActivityFunc="demandPlanActivityFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
            />

            <!-- 保险 -->
            <insurance-com
              :id="'insuranceId' + dateIndex"
              ref="demandInsuranceRef"
              :date-index="dateIndex"
              :meetingPersonTotal="props.meetingPersonTotal"
              :insurance-list="dateItem.insurances"
              :insurance-product-list="insuranceProductList"
              :insurance-loading="insuranceLoading"
              :insurance-has-more="insuranceHasMore"
              @demandPlanInsuranceFunc="demandPlanInsuranceFunc"
              @demandPlanRemoveFunc="demandPlanRemoveFunc"
              @insurancePopupScroll="handleInsurancePopupScroll"
              @insuranceSearch="handleInsuranceSearch"
            />
          </div>

          <div :class="[isPlanEmpty[dateIndex] ? 'plan_btns_empty' : 'plan_btns', 'mt20']">
            <a-dropdown :trigger="['click']" arrow placement="top">
              <a-button v-if="isPlanEmpty[dateIndex]" class="plan_add_btn_empty">
                <template #icon>
                  <div class="plan_add_img_empty"></div>
                </template>
                <span class="ml8">添加新需求</span>
              </a-button>
              <a-button v-else class="plan_add_btn" type="primary">
                <template #icon>
                  <div class="plan_add_img mr8"></div>
                </template>
                <span>添加新需求</span>
              </a-button>

              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-for="(item, idx) in demandTypeList"
                    :key="item.code"
                    @click="planAdd(dateItem.demandDate, item.type, dateIndex)"
                  >
                    <div :class="'demand_plan_add' + (idx + 1)">
                      <span>{{ item.desc }} </span>
                    </div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 复制需求 - 弹窗 -->
    <a-modal v-model:open="planShow" title="复制需求" width="400px" @ok="handleCopy" @cancel="closeModal">
      <div>
        <a-select
          class="mt24 mb24"
          v-model:value="copyData"
          placeholder="请选择要复制的需求日期"
          allow-clear
          style="width: 100%"
        >
          <a-select-option
            v-for="(item, index) in dateRangeList"
            :key="index"
            :value="item"
            :disabled="isPlanEmpty[index]"
            @click="copyChange(index)"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </div>
    </a-modal>

    <!-- 需求覆盖 - 弹窗 -->
    <a-modal
      v-model:open="coverShow"
      title="温馨提示"
      width="400px"
      @ok="handleCover"
      @cancel="handleNotCover"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      :centered="true"
    >
      <div>请问您是否要接收已录入数据？</div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.demand_plan {
  padding: 24px 16px;

  .demand_plan_date {
    position: absolute;
    top: 0;
    right: 0;

    width: 256px;
    height: 32px;
  }

  .date_plan {
    .plan_collapse {
      background: #fff;

      .plan_collapse_arrow {
        cursor: pointer;
        width: 14px;
        height: 14px;
        background: url('@/assets/image/demand/demand_expand.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }

      .plan_collapse_title {
        font-weight: 500;
        font-size: 18px;
        color: #1d2129;
      }

      .plan_collapse_extra {
        display: flex;
        align-items: center;

        .extra_copy {
          cursor: pointer;
          background: url('@/assets/image/demand/demand_copy_blue.png');
          background-repeat: no-repeat;
          background-size: 16px 16px;
          background-position: left center;

          text-indent: 26px;
          color: #1868db;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .plan_collapse_content {
      }

      .plan_btns {
        user-select: none;

        .plan_add_btn {
          padding: 0 15px;
          width: 132px;
          height: 36px;
          box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
          border-radius: 4px;
          display: flex;
          align-items: center;

          font-weight: 500;
          font-size: 14px;
          color: #fff;

          .plan_add_img {
            width: 20px;
            height: 20px;
            background: url('@/assets/image/demand/demand_add_white.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
      }

      .plan_btns_empty {
        margin: 44px 0 94px;
        user-select: none;

        .plan_add_btn_empty {
          margin: 0 auto;
          padding: 0 68px;
          width: 232px;
          height: 40px;
          background: #ffffff;
          border-radius: 4px;
          border: 2px solid rgba(24, 104, 219, 0.5);
          display: flex;
          align-items: center;

          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #1868db;
          text-shadow: 0px 2px 8px rgba(0, 103, 216, 0.1);

          .plan_add_img_empty {
            margin-top: -2px;
            width: 16px;
            height: 16px;
            background: url('@/assets/image/demand/demand_add_blue.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }

    /* 折叠面板 */
    :deep(.ant-collapse-item) {
      background: #f6f9fc;
      border-radius: 8px;
      border: 1px solid #e5e6e8;
      overflow: hidden;
    }
    :deep(.ant-collapse-header) {
      /* 头部 */
      padding: 0 32px 0 24px;
      height: 50px;
      line-height: 50px;
      background: #e6f0ff;
      border-radius: 8px 8px 0px 0px;
    }
    :deep(.ant-collapse-content) {
      /* 内容 */
      padding: 20px 24px;
    }
    :deep(.ant-collapse .ant-collapse-content > .ant-collapse-content-box) {
      padding: 0;
    }
    :deep(.ant-collapse-expand-icon) {
      /* 图标 */
      height: 100%;
      padding-inline-end: 10px;
    }
  }
}

.demand_plan_add1 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_stay.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add2 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_place.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add3 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_catering.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add4 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_vehicle.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add5 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_attendant.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add6 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_activity.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
.demand_plan_add7 {
  text-indent: 24px;
  background: url('@/assets/image/demand/demand_insurance.png');
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center left;
}
</style>
