<script setup lang="ts">
// 方案互动-会场方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  HotelsArr,
  LedSourceTypeConstant,
  PlacesArr,
} from '@haierbusiness-front/common-libs';

import schemeGuildhall from './../schemeGuildhall.vue';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  demandHotels: {
    type: Array,
    default: [],
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemePlacesEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 会场
const placesParams = ref<PlacesArr>({
  tempDemandHotelId: null, // 需求酒店id
  level: null,
  demandDate: '', // 需求日期
  usageTime: null, // 使用时间
  usagePurpose: null, // 使用用途
  personNum: null, // 人数
  area: null, // 面积
  underLightFloor: null, // 灯下层高
  tableType: null, // 摆台形式

  hasLed: false, // 是否需要led
  ledNum: null, // led数量
  ledSpecs: null, // led规格说明

  hasTea: null, // 是否需要茶歇
  teaEachTotalPrice: null, // 茶歇标准/每人
  teaDesc: null, // 茶歇说明

  calcUnitPlacePrice: null, // 自动测算会场单价
  calcUnitLedPrice: null, // 自动测算led单价
  calcUnitTeaPrice: null, // 自动测算茶歇单价
});

// 会议厅
const guildhallRef = ref(null);
const placeModalShow = ref<boolean>(false); // 会厅弹窗
const guildhallIndex = ref<number>(0); // 会厅index
const guildhallName = ref<string>(''); // 会厅名称
const guildhallPhotoList = ref<array>([]); // 会厅图片列表
const visible = ref<boolean>(false);
const guildhallImgs = ref<array>([]); // 会厅图片列表

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every((e) => e.schemeUnitPlacePrice && e.schemeUnitPlacePrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.schemeUnitPlacePrice;

      if (e.hasLed) {
        // 单价*LED数量
        subtotal.value += e.schemeUnitLedPrice * e.schemeLedNum;
      }
      if (e.hasTea) {
        // 茶歇单价*会场人数
        subtotal.value += e.teaEachTotalPrice * e.schemePersonNum;
      }
    });

    emit('schemePriceEmit', { type: 'place', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

watch(
  () => [props.hotels, props.merchantType, newSchemeList.value],
  () => {
    newSchemeList.value.forEach((e) => {
      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1 ? props.hotels[0].tempId : e.miceSchemeHotelId || props.hotels[0].tempId;
        e.miceDemandPushHotelId = props.hotels[0].miceDemandPushHotelId;
      } else {
        // if (e.miceSchemeHotelId) e.tempSchemeHotelId = e.miceSchemeHotelId;
      }
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = [
  '酒店选择',
  '会议厅选择',
  '使用时间',
  '会场用途',
  '人数',
  '摆台形式',
  '面积',
  '层高',
  'LED数量',
  'LED单价',
  'LED来源',
  'LED规格描述',
  '茶歇标准',
  '茶歇说明',
  '备注',
];

// 酒店名称
const hotelName = (hotelItem: HotelsArr) => {
  let str = '-';

  props.demandHotels.forEach((e, index) => {
    if (e.id && e.id === hotelItem.miceDemandHotelId) {
      str = `酒店${index + 1}(${
        (e.centerMarker ? e.centerMarker : e.provinceName + e.cityName + e.districtNames) +
        '/' +
        (hotelLevelAllConstant.ofType(e.level)?.desc || '-')
      })`;
    }
  });

  return str;
};

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 会厅图片查看
const guildhallImgView = (photos: array) => {
  guildhallImgs.value = photos.map((e) => {
    const obj = JSON.parse(e);

    return obj.url;
  });
  visible.value = true;
};
// 会厅选择
const guildhallAdd = (name: string, photos: array, index: number) => {
  guildhallIndex.value = index;
  guildhallName.value = name;
  guildhallPhotoList.value = photos;

  placeModalShow.value = true;
};
const guildhallFunc = (data: PlacesArr) => {
  newSchemeList.value[guildhallIndex.value].guildhall = data.guildhallName;
  newSchemeList.value[guildhallIndex.value].guildhallPhotos =
    data.guildhallPhotos?.length > 0
      ? data.guildhallPhotos.map((e) => {
          const params = {
            name: e.name,
            url: e.filePath,
          };

          return JSON.stringify(params);
        })
      : [];
};
const placeConfirm = async () => {
  let isVerifyPassed = await guildhallRef?.value.onSubmit();

  if (isVerifyPassed) placeCancel();
};
const placeCancel = () => {
  placeModalShow.value = false;
};

// 暂存
const placeTempSave = () => {
  emit('schemePlacesEmit', {
    schemePlaces: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const placeSub = () => {
  let isPlaceVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isPlaceVerPassed === false) return;

    if (!e.tempSchemeHotelId) {
      message.error('请选择' + e.demandDate + '会场' + (i + 1) + '酒店');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }

    if (!e.guildhall) {
      message.error('请填写' + e.demandDate + '会场' + (i + 1) + '会议厅名称');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }

    if (e.hasLed && (e.schemeUnitLedPrice === null || e.schemeUnitLedPrice === undefined)) {
      message.error('请填写' + e.demandDate + '会场' + (i + 1) + 'LED单价');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }

    if (e.hasLed && (e.schemeLedSource === null || e.schemeLedSource === undefined)) {
      message.error('请选择' + e.demandDate + '会场' + (i + 1) + 'LED来源');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }

    if (!e.schemeUnitPlacePrice) {
      message.error('请输入' + e.demandDate + '会场' + (i + 1) + '单价');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }
  });

  if (isPlaceVerPassed) {
    placeTempSave();
  }

  return isPlaceVerPassed;
};

defineExpose({ placeSub, placeTempSave });

onMounted(async () => {
  // console.log('%c [ 会场 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeItem.places);

  oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.places || [];

  if (props.isSchemeCache && props.schemeCacheItem) {
    // 缓存 - 反显
    newSchemeList.value = props.schemeCacheItem?.places || [];

    newSchemeList.value.forEach((e) => {
      e.placeNum = 1; // 会厅数量

      if (e.miceSchemeHotelId) e.tempSchemeHotelId = e.miceSchemeHotelId;
    });

    // 价格计算
    priceCalcFun();
  } else {
    const demandPlaces = JSON.parse(JSON.stringify(props.schemeItem))?.places || [];
    newSchemeList.value = demandPlaces.map((e) => {
      return {
        tempSchemeHotelId: props.hotels && props.hotels.length === 1 ? props.hotels[0].tempId : null,
        miceDemandPushHotelId: props.hotels && props.hotels.length === 1 ? props.hotels[0].miceDemandPushHotelId : null,
        miceDemandPlaceId: e.id,

        demandDate: e.demandDate,
        usageTime: e.usageTime,
        usagePurpose: e.usagePurpose,
        schemePersonNum: e.personNum,
        underLightFloor: e.underLightFloor,
        tableType: e.tableType,
        placeNum: 1, // 会厅数量

        hasLed: e.hasLed,
        schemeLedNum: e.ledNum,
        schemeLedSource: null,
        ledSpecs: e.ledSpecs,

        hasTea: e.hasTea,
        teaEachTotalPrice: e.teaEachTotalPrice,
        teaDesc: e.teaDesc,

        description: e.description,

        guildhall: null, // 会议厅
        guildhallPhotos: [], // 会议厅图片
        schemeUnitPlacePrice: null, // 方案报价会场单价
        schemeUnitLedPrice: null, //
        schemeUnitTeaPrice: e.teaEachTotalPrice, //
        area: e.area,
      };
    });
  }
});
</script>

<template>
  <!-- 会场方案 -->
  <div class="scheme_place">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>会场需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ hotelName(item) }}
                </template>
                {{ hotelName(item) }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </template>
                {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </template>
                {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.personNum ? item.personNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </template>
                {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.area ? item.area + '㎡' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.ledNum ? item.ledNum + '个' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">-</div>
            <div class="scheme_plan_value pl12">-</div>
            <div class="scheme_plan_value pl12">
              {{ item.ledSpecs || '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaDesc || '-' }}
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>
        </div>
      </div>

      <!-- 竞价 - 标的方案 -->
      <div class="common_table_l" v-else v-show="showBindingScheme">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>会场方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 p0">
              <a-select
                v-model:value="item.tempSchemeHotelId"
                style="width: 100%"
                :disabled="
                  (props.hotels && props.hotels.length === 1) ||
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
                placeholder="请选择酒店"
                :bordered="false"
                :dropdownMatchSelectWidth="300"
                allow-clear
              >
                <a-select-option v-for="(item, idx) in props.hotels" :key="item.tempId" :value="item.tempId">
                  <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                    {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
            </div>
            <div class="scheme_plan_value pl12 pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.guildhall }}
                </template>
                {{ item.guildhall }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </template>
                {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </template>
                {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </template>
                {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.area ? item.area + '㎡' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.schemeLedNum ? item.schemeLedNum + '个' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">-</div>
            <div class="scheme_plan_value pl12 p0">
              <div class="pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                  </template>
                  {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.ledSpecs || '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaDesc || '-' }}
            </div>
            <div class="scheme_plan_value pl12 pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>会场方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.tempSchemeHotelId ? 'error_tip' : '']">
                <a-select
                  v-model:value="item.tempSchemeHotelId"
                  style="width: 100%"
                  :disabled="
                    (props.hotels && props.hotels.length === 1) ||
                    props.schemeType === 'notBidding' ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView'
                  "
                  placeholder="请选择酒店"
                  :bordered="false"
                  :dropdownMatchSelectWidth="300"
                  allow-clear
                >
                  <a-select-option v-for="(item, idx) in props.hotels" :key="item.tempId" :value="item.tempId">
                    <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                      {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.guildhall ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    props.schemeType === 'notBidding' ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView'
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.guildhall }}
                    </template>
                    {{ item.guildhall }}
                  </a-tooltip>
                </div>
                <div v-else @click="guildhallAdd(item.guildhall, item.guildhallPhotos, idx)">
                  <a-input
                    v-model:value="item.guildhall"
                    readonly
                    style="width: calc(100% - 66px)"
                    placeholder="请填写会厅"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_view_img" @click.stop="guildhallImgView(item.guildhallPhotos)">
                    {{ item.guildhallPhotos && item.guildhallPhotos.length > 0 ? '查看' : '' }}
                  </div>
                  <div class="scheme_plan_edit"></div>

                  <!-- 会厅图片预览 -->
                  <div style="display: none">
                    <a-image-preview-group :preview="{ visible, onVisibleChange: (vis) => (visible = vis) }">
                      <a-image v-for="url in guildhallImgs" :key="url" :src="url" />
                    </a-image-preview-group>
                  </div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                  </template>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                  </template>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                  </template>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.area ? item.area + '㎡' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.schemeLedNum ? item.schemeLedNum + '个' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed &&
                  item.hasLed &&
                  (item.schemeUnitLedPrice === null || item.schemeUnitLedPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView' || !item.hasLed"
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeUnitLedPrice || '-' }}
                    </template>
                    {{ item.schemeUnitLedPrice || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeUnitLedPrice"
                    @blur="changePrice(idx)"
                    placeholder="LED单价"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && item.hasLed && (item.schemeLedSource === null || item.schemeLedSource === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    props.schemeType === 'notBidding' ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    !item.hasLed
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                    </template>
                    {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.schemeLedSource"
                  style="width: 100%"
                  placeholder="请选择LED来源"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in LedSourceTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.ledSpecs || '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.teaDesc || '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                class="pl12"
                v-if="
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>

              <a-tooltip placement="topLeft" v-else>
                <template #title v-if="item.description">
                  {{ item.description || '-' }}
                </template>
                <a-input
                  v-model:value="item.description"
                  style="width: calc(100% - 30px)"
                  placeholder="备注"
                  :maxlength="500"
                  :bordered="false"
                  allow-clear
                />
                <div class="scheme_plan_edit"></div>
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemePlaceId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                {{ '¥' + formatNumberThousands(item.schemeUnitPlacePrice) }}
              </div>
              <div
                :class="[
                  'scheme_plan_price_value',
                  isVerifyFailed && !item.schemeUnitPlacePrice ? 'error_price_tip' : '',
                ]"
                v-else
              >
                <a-input-number
                  v-model:value="item.schemeUnitPlacePrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPlacePrice
                    ? formatNumberThousands(
                        item.schemeUnitPlacePrice +
                          (item.hasLed ? item.schemeUnitLedPrice * item.schemeLedNum : 0) +
                          (item.hasTea ? item.teaEachTotalPrice * item.schemePersonNum : 0),
                      )
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPlacePrice">
                <div>
                  {{ item.schemeUnitPlacePrice + '(会场价格)' }}
                </div>
                <div
                  v-if="
                    item.hasLed &&
                    item.schemeLedNum !== null &&
                    item.schemeLedNum !== undefined &&
                    item.schemeUnitLedPrice !== null &&
                    item.schemeUnitLedPrice !== undefined
                  "
                >
                  {{ item.schemeLedNum + '(数量)*' + item.schemeUnitLedPrice + '(LED单价)' }}
                </div>
                <div
                  v-if="
                    item.hasTea &&
                    item.schemePersonNum !== null &&
                    item.schemePersonNum !== undefined &&
                    item.teaEachTotalPrice !== null &&
                    item.teaEachTotalPrice !== undefined
                  "
                >
                  {{ item.schemePersonNum + '(人数)*' + item.teaEachTotalPrice + '(茶歇单价)' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-show="newSchemeList.length > 0" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>

    <!-- 会厅 - 弹窗 -->
    <a-modal
      v-model:open="placeModalShow"
      title="会厅选择"
      width="700px"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <schemeGuildhall
          ref="guildhallRef"
          :guildhall="guildhallName"
          :guildhallPhotos="guildhallPhotoList"
          @guildhallFunc="guildhallFunc"
        />
      </div>
      <template #footer>
        <a-button @click="placeCancel">取消</a-button>
        <a-button type="primary" @click="placeConfirm">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.scheme_place {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_place.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }

    .scheme_plan_view_img {
      display: inline-flex;
      width: 36px;
      height: 100%;
      text-align: center;
      color: #1868db;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
  .pr0 {
    padding-right: 0 !important;
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
