import { download, get, post } from '../request'
import {
    IPascalCaseFilter,
    IPascalCase,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const pascalCaseApi = {
    // 礼品分页列表
    list: (params: IPascalCaseFilter): Promise<IPageResponse<IPascalCase>> => {
        return get('/mice-bid/api/mice/merchant/product/present/page', params)
    },
    // 需求提报页的列表
    listPushDemand: (params: IPascalCaseFilter): Promise<IPageResponse<IPascalCase>> => {
        return get('/mice-bid/api/mice/user/product/present/pushDemandPage', params)
    },
    // 礼品详情
    get: (id: number): Promise<IPascalCase> => {
        return get('/mice-bid/api/mice/user/product/present/detail', {
            id
        })
    },
    // 礼品详情
    details: (id: number): Promise<IPascalCase> => {
        return get('/mice-bid/api/mice/merchant/product/present/adminDetail', {
            id
        })
    },
    // 新增礼品数据接口
    save: (params: IPascalCase): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/product/present/add', params)
    },

    // 礼品上架下架礼品
    updateStatus: (params: IPascalCase): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/product/present/stopSale', params)
    },


    // 保险分页列表
    insuranceList: (params: IPascalCaseFilter): Promise<IPageResponse<IPascalCase>> => {
        return get('/mice-bid/api/mice/merchant/product/insurance/page', params)
    },
    // 保险详情
    insuranceGet: (id: number): Promise<IPascalCase> => {
        return get('/mice-bid/api/mice/user/product/insurance/detail', {
            id
        })
    },
    // 保险产品新增接口
    insuranceSave: (params: IPascalCase): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/product/insurance/add', params)
    },
    // 保险产品编辑接口
    insuranceEdit: (params: IPascalCase): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/product/insurance/edit', params)
    },
    // 新增保险删除
    insuranceRemove: (id: number): Promise<Result> => {
        return post(`/mice-bid/api/mice/merchant/product/insurance/delete?id=${id}`)
    },
    //获取产品信息酒店列表
    hotelList: (params: IPascalCaseFilter): Promise<IPageResponse<IPascalCase>> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/product-message', params)
    },

    //礼品编辑
    presentEdit: (params: IPascalCase): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/product/present/edit', params)
    },
}
